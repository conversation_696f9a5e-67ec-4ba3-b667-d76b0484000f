package public

import (
	"context"
	"fmt"
	"path/filepath"
	"strings"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"realmaster-video-backend/internal/config"
	"realmaster-video-backend/internal/domain/state"
	"realmaster-video-backend/internal/domain/video"
	"realmaster-video-backend/internal/platform/logger"
)

// Service 定义公共API服务接口
type Service interface {
	GetFeed(ctx context.Context, userID string, page, limit int) (*FeedResponse, error)
	GetUserStates(ctx context.Context, userID string, videoIDs []string) ([]state.StateResponse, error)
	GetUserFavorites(ctx context.Context, userID string, page, limit int) (*UserFavoritesResponse, error)
}

type service struct {
	videoRepo video.Repository
	stateRepo state.Repository
	cfg       *config.Config
}

// NewService 创建新的公共API服务实例
func NewService(videoRepo video.Repository, stateRepo state.Repository, cfg *config.Config) Service {
	return &service{
		videoRepo: videoRepo,
		stateRepo: stateRepo,
		cfg:       cfg,
	}
}

// FeedResponse Feed接口的响应结构
type FeedResponse struct {
	Videos     []video.VideoResponse `json:"videos"`
	Pagination PaginationResponse    `json:"pagination"`
}

// UserFavoritesResponse 用户收藏响应结构
type UserFavoritesResponse struct {
	Favorites  []state.FavoriteVideoResponse `json:"favorites"`
	Pagination PaginationResponse            `json:"pagination"`
}

// PaginationResponse 分页响应结构
type PaginationResponse struct {
	TotalItems   int64 `json:"totalItems"`
	TotalPages   int64 `json:"totalPages"`
	CurrentPage  int64 `json:"currentPage"`
	ItemsPerPage int64 `json:"itemsPerPage"`
}

// GetFeed 获取视频Feed（已发布的视频，按发布时间倒序）
func (s *service) GetFeed(ctx context.Context, userID string, page, limit int) (*FeedResponse, error) {
	// 设置默认分页参数
	if page <= 0 {
		page = 1
	}
	if limit <= 0 {
		limit = 20
	}

	// 构建查询过滤器：只返回已发布的视频
	filter := video.VideoFilter{
		Status: []string{video.StatusPublished},
		Page:   page,
		Limit:  limit,
	}

	// 查询视频列表
	videos, totalCount, err := s.videoRepo.Find(ctx, filter)
	if err != nil {
		logger.Log.Error("查询Feed视频失败",
			logger.Error(err),
			logger.String("userId", userID),
			logger.Int("page", page),
			logger.Int("limit", limit),
		)
		return nil, fmt.Errorf("查询Feed视频失败: %w", err)
	}

	// 转换为响应格式
	var videoResponses []video.VideoResponse
	for _, v := range videos {
		videoResponses = append(videoResponses, s.toVideoResponse(&v))
	}

	// 计算分页信息
	totalPages := (totalCount + int64(limit) - 1) / int64(limit)

	response := &FeedResponse{
		Videos: videoResponses,
		Pagination: PaginationResponse{
			TotalItems:   totalCount,
			TotalPages:   totalPages,
			CurrentPage:  int64(page),
			ItemsPerPage: int64(limit),
		},
	}

	logger.Log.Info("成功获取Feed",
		logger.String("userId", userID),
		logger.Int("page", page),
		logger.Int("limit", limit),
		logger.Int64("totalCount", totalCount),
		logger.Int("returnedCount", len(videoResponses)),
	)

	return response, nil
}

// GetUserStates 批量获取用户对视频的状态
func (s *service) GetUserStates(ctx context.Context, userID string, videoIDs []string) ([]state.StateResponse, error) {
	// 转换用户ID
	userObjectID, err := s.convertUserID(userID)
	if err != nil {
		return nil, fmt.Errorf("无效的用户ID: %w", err)
	}

	// 转换视频ID列表
	var videoObjectIDs []primitive.ObjectID
	for _, videoIDStr := range videoIDs {
		videoObjectID, err := primitive.ObjectIDFromHex(videoIDStr)
		if err != nil {
			logger.Log.Warn("跳过无效的视频ID",
				logger.String("videoId", videoIDStr),
				logger.Error(err),
			)
			continue
		}
		videoObjectIDs = append(videoObjectIDs, videoObjectID)
	}

	if len(videoObjectIDs) == 0 {
		return []state.StateResponse{}, nil
	}

	// 批量查询状态
	states, err := s.stateRepo.FindBatchByUser(ctx, userObjectID, videoObjectIDs)
	if err != nil {
		logger.Log.Error("批量查询用户状态失败",
			logger.Error(err),
			logger.String("userId", userID),
			logger.Int("videoCount", len(videoIDs)),
		)
		return nil, fmt.Errorf("批量查询用户状态失败: %w", err)
	}

	// 转换为响应格式
	var responses []state.StateResponse
	for _, s := range states {
		responses = append(responses, state.StateResponse{
			UserID:          s.ID.UserID,
			VideoID:         s.ID.VideoID,
			Liked:           s.Liked,
			Favorited:       s.Favorited,
			Blocked:         s.Blocked,
			ProgressSeconds: s.ProgressSeconds,
			// TODO: 使用gomongo的_mt时间戳
			// ModifiedTime:    s.ModifiedTime,
		})
	}

	logger.Log.Info("成功批量获取用户状态",
		logger.String("userId", userID),
		logger.Int("requestedCount", len(videoIDs)),
		logger.Int("returnedCount", len(responses)),
	)

	return responses, nil
}

// GetUserFavorites 获取用户收藏的视频
func (s *service) GetUserFavorites(ctx context.Context, userID string, page, limit int) (*UserFavoritesResponse, error) {
	// 设置默认分页参数
	if page <= 0 {
		page = 1
	}
	if limit <= 0 {
		limit = 20
	}

	// 转换用户ID
	userObjectID, err := s.convertUserID(userID)
	if err != nil {
		return nil, fmt.Errorf("无效的用户ID: %w", err)
	}

	// 查询用户收藏
	states, totalCount, err := s.stateRepo.FindUserFavorites(ctx, userObjectID, int64(page), int64(limit))
	if err != nil {
		logger.Log.Error("查询用户收藏失败",
			logger.Error(err),
			logger.String("userId", userID),
			logger.Int("page", page),
			logger.Int("limit", limit),
		)
		return nil, fmt.Errorf("查询用户收藏失败: %w", err)
	}

	// 转换为响应格式
	var favorites []state.FavoriteVideoResponse
	for _, s := range states {
		favorites = append(favorites, state.FavoriteVideoResponse{
			VideoID:         s.ID.VideoID,
			ProgressSeconds: s.ProgressSeconds,
			// TODO: 使用gomongo的_mt时间戳
			// ModifiedTime:    s.ModifiedTime,
		})
	}

	// 计算分页信息
	totalPages := (totalCount + int64(limit) - 1) / int64(limit)

	response := &UserFavoritesResponse{
		Favorites: favorites,
		Pagination: PaginationResponse{
			TotalItems:   totalCount,
			TotalPages:   totalPages,
			CurrentPage:  int64(page),
			ItemsPerPage: int64(limit),
		},
	}

	logger.Log.Info("成功获取用户收藏",
		logger.String("userId", userID),
		logger.Int("page", page),
		logger.Int("limit", limit),
		logger.Int64("totalCount", totalCount),
		logger.Int("returnedCount", len(favorites)),
	)

	return response, nil
}

// toVideoResponse 将Video模型转换为VideoResponse
func (s *service) toVideoResponse(v *video.Video) video.VideoResponse {
	resp := video.VideoResponse{
		ID:          v.ID,
		Title:       v.Title,
		Description: v.Description,
		Status:      v.Status,
		Duration:    v.Duration,
		UploaderID:  v.UploaderID,
		CategoryID:  v.CategoryID,
		Tags:        v.Tags,
		PropertyIDs: v.PropertyIDs,
		ExternalURL: v.ExternalURL,
		ClientID:    v.ClientID,
		Stats:       v.Stats,
		PublishedAt: v.PublishedAt,
	}

	// 根据视频状态生成预览URL，使用goupload路径
	switch v.Status {
	case video.StatusPublished:
		// 对于已发布的视频，生成公开可访问的URL
		if v.FinalThumbGouploadPath != "" {
			resp.PreviewThumbUrl = s.buildGouploadURL("thumbnail_final", v.FinalThumbGouploadPath)
		}
		if v.FinalVideoGouploadPath != "" {
			// 对于已处理的视频，previewVideoUrl返回HLS文件路径
			resp.PreviewVideoUrl = s.buildGouploadURL("video_final", v.FinalVideoGouploadPath)
		}
	case video.StatusUnpublished:
		// 对于未发布的视频，也生成URL但可能需要特殊处理
		if v.FinalThumbGouploadPath != "" {
			resp.PreviewThumbUrl = s.buildGouploadURL("thumbnail_final", v.FinalThumbGouploadPath)
		}
		if v.FinalVideoGouploadPath != "" {
			resp.PreviewVideoUrl = s.buildGouploadURL("video_final", v.FinalVideoGouploadPath)
		}
	}

	return resp
}

// buildGouploadURL 基于goupload路径和entryName构建完整的访问URL
func (s *service) buildGouploadURL(entryName, gouploadPath string) string {
	if gouploadPath == "" {
		return ""
	}

	// 确保基础URL包含协议
	baseURL := s.cfg.Media.ServerURL
	if !strings.HasPrefix(baseURL, "http://") && !strings.HasPrefix(baseURL, "https://") {
		baseURL = "http://" + baseURL
	}

	// 根据entryName确定URL前缀（对应nginx配置）
	var prefix string
	switch entryName {
	case "video_draft":
		prefix = "/draft/videos"
	case "thumbnail_draft":
		prefix = "/draft/thumbnails"
	case "video_final":
		prefix = "/media/videos"
	case "thumbnail_final":
		prefix = "/media/thumbnails"
	default:
		return ""
	}

	// gouploadPath 格式: USER/2025-28/abc123/filename.ext
	return fmt.Sprintf("%s%s/%s", baseURL, prefix, gouploadPath)
}

// convertUserID 将字符串用户ID转换为ObjectID（与interaction service中的逻辑一致）
func (s *service) convertUserID(userIDStr string) (primitive.ObjectID, error) {
	// 如果是ObjectID格式，直接转换
	if objectID, err := primitive.ObjectIDFromHex(userIDStr); err == nil {
		return objectID, nil
	}

	// 如果是字符串格式（如dev_admin_001），需要转换
	return s.generateObjectIDFromString(userIDStr), nil
}

// generateObjectIDFromString 从字符串生成一致的ObjectID（与interaction service中的逻辑一致）
func (s *service) generateObjectIDFromString(str string) primitive.ObjectID {
	hash := 0
	for _, char := range str {
		hash = hash*31 + int(char)
	}

	bytes := make([]byte, 12)
	for i := 0; i < 12; i++ {
		bytes[i] = byte(hash >> (i * 8))
	}

	return primitive.ObjectID(bytes)
}

// buildFinalMediaURL 构建最终媒体文件的完整URL
func (s *service) buildFinalMediaURL(v *video.Video, fileName string) string {
	// 确保基础URL包含协议，以生成一个绝对URL
	baseURL := s.cfg.Media.ServerURL
	if !strings.HasPrefix(baseURL, "http://") && !strings.HasPrefix(baseURL, "https://") {
		baseURL = "http://" + baseURL
	}

	// 获取相对路径
	relativePath := s.getFinalMediaRelativePath(v.ID.Hex(), v.Status)

	// 构建完整URL
	return fmt.Sprintf("%s/media/%s/%s", baseURL, relativePath, filepath.Base(fileName))
}

// getFinalMediaRelativePath 返回用于URL构建的相对路径
func (s *service) getFinalMediaRelativePath(videoIDHex string, status string) string {
	// 这里简化处理，直接使用videoID作为目录名
	// 在实际项目中，这里应该实现与video service中相同的L1/L2逻辑
	dirName := videoIDHex
	if status == video.StatusUnpublished {
		dirName = "unpublished_" + dirName
	}
	return dirName
}
