package fileupload

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"

	"realmaster-video-backend/internal/config"
	"realmaster-video-backend/internal/domain/common"
	"realmaster-video-backend/internal/domain/video"
	"realmaster-video-backend/internal/platform/logger"

	"github.com/gin-gonic/gin"
	"github.com/real-rm/gofile/levelStore"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var (
	ErrMissingVideoID  = errors.New("URL中缺少videoID参数")
	ErrMissingFileName = errors.New("URL中缺少fileName参数")
	ErrMissingFile     = errors.New("请求中缺少文件")
	ErrCreateDirectory = errors.New("无法创建存储目录")
	ErrCreateFile      = errors.New("无法创建目标文件")
	ErrSaveFile        = errors.New("保存文件时出错")
)

// FileUploadHandler 封装了文件上传的依赖
type FileUploadHandler struct {
	storageDir string
	videoRepo  video.Repository
}

// NewFileUploadHandler 创建一个新的 FileUploadHandler
func NewFileUploadHandler(cfg *config.Config, videoRepo video.Repository) *FileUploadHandler {
	return &FileUploadHandler{
		storageDir: cfg.Media.StorageDir,
		videoRepo:  videoRepo,
	}
}

// getVideoL1L2Path 根据视频ID生成L1/L2路径
func (h *FileUploadHandler) getVideoL1L2Path(ctx context.Context, videoIDHex string) (string, error) {
	// 从ObjectID中提取创建时间（ObjectID包含时间戳信息）
	objectID, err := primitive.ObjectIDFromHex(videoIDHex)
	if err != nil {
		logger.Log.Error("Invalid ObjectID for file upload path",
			logger.String("videoID", videoIDHex), logger.Error(err))
		return "", fmt.Errorf("invalid video ID: %w", err)
	}

	createdAt := objectID.Timestamp()
	// 使用gofile包生成L1/L2路径 - 视频文件使用EDM board
	l1, l2, err := levelStore.GetL1L2Separate(createdAt, "EDM", videoIDHex)
	if err != nil {
		// 如果L1/L2生成失败，回退到原有逻辑
		logger.Log.Error("Failed to generate L1/L2 path for worker upload, falling back to legacy path",
			logger.String("videoID", videoIDHex), logger.Error(err))
		return videoIDHex, nil
	}

	// 构建L1/L2分层目录结构
	return filepath.Join("EDM", l1, l2, videoIDHex), nil
}

// HandleUpload 是处理文件上传的核心方法
func (h *FileUploadHandler) HandleUpload(c *gin.Context) {
	// 从 URL 中获取 videoID 和 filename
	videoID := c.Param("videoID")
	fileName := c.Param("fileName")
	if videoID == "" {
		common.SendError(c, http.StatusBadRequest, ErrMissingVideoID.Error(), ErrMissingVideoID.Error())
		return
	}
	if fileName == "" {
		common.SendError(c, http.StatusBadRequest, ErrMissingFileName.Error(), ErrMissingFileName.Error())
		return
	}

	// 获取L1/L2路径
	l1l2Path, err := h.getVideoL1L2Path(c.Request.Context(), videoID)
	if err != nil {
		logger.Log.Error("获取L1/L2路径失败", logger.String("videoID", videoID), logger.Error(err))
		common.SendError(c, http.StatusInternalServerError, "获取视频路径失败", err.Error())
		return
	}

	// 构建目标目录路径并确保它存在
	// e.g., /var/www/rm_video_media/USER/1250/6a5cf/666f72646572
	targetDir := filepath.Join(h.storageDir, l1l2Path)
	if err := os.MkdirAll(targetDir, 0755); err != nil {
		logger.Log.Error("创建目标目录失败", logger.String("dir", targetDir), logger.Error(err))
		common.SendError(c, http.StatusInternalServerError, ErrCreateDirectory.Error(), err.Error())
		return
	}

	// 构建最终的文件保存路径
	// e.g., /var/www/rm_video_media/666f72646572/my_video_1080p.mp4
	destinationPath := filepath.Join(targetDir, fileName)

	// 从请求体中获取文件
	// 注意：worker端使用的是"file"作为form-data的字段名
	file, _, err := c.Request.FormFile("file")
	if err != nil {
		logger.Log.Error("从请求中获取文件失败", logger.Error(err))
		common.SendError(c, http.StatusBadRequest, ErrMissingFile.Error(), err.Error())
		return
	}
	defer file.Close()

	// 创建目标文件
	outFile, err := os.Create(destinationPath)
	if err != nil {
		logger.Log.Error("创建目标文件失败", logger.String("path", destinationPath), logger.Error(err))
		common.SendError(c, http.StatusInternalServerError, ErrCreateFile.Error(), err.Error())
		return
	}
	defer outFile.Close()

	// 将上传的文件内容拷贝到目标文件
	_, err = io.Copy(outFile, file)
	if err != nil {
		logger.Log.Error("写入文件内容失败", logger.String("path", destinationPath), logger.Error(err))
		common.SendError(c, http.StatusInternalServerError, ErrSaveFile.Error(), err.Error())
		return
	}

	logger.Log.Info("文件已成功保存",
		logger.String("videoID", videoID),
		logger.String("path", destinationPath),
	)

	common.SendSuccess(c, http.StatusCreated, gin.H{"message": "文件上传成功", "path": destinationPath})
}
